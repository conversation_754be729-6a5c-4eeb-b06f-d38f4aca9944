<template>
  <div class="scenic-list">
    <div class="page-header">
      <h2>景区管理</h2>
      <el-button type="primary" @click="handleCreate">
        <el-icon><Plus /></el-icon>
        新增景区
      </el-button>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="景区标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入景区标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="所属省份">
          <el-select
            v-model="searchForm.provinceId"
            placeholder="请选择省份"
            clearable
            style="width: 150px"
            @change="handleProvinceChange"
          >
            <el-option
              v-for="province in provinceList"
              :key="province.id"
              :label="province.name"
              :value="province.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属城市">
          <el-select
            v-model="searchForm.cityId"
            placeholder="请选择城市"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="city in cityList"
              :key="city.id"
              :label="city.name"
              :value="city.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        <el-table-column prop="scenicId" label="景区ID" width="120" />
        <el-table-column prop="title" label="景区标题" min-width="150" />
        <el-table-column prop="subtitle" label="副标题" min-width="120" />
        <el-table-column prop="image" label="封面图" width="100">
          <template #default="{ row }">
            <el-image
              v-if="row.image"
              :src="row.image"
              style="width: 60px; height: 40px"
              fit="cover"
              :preview-src-list="[row.image]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="80" />
        <el-table-column prop="rating" label="评分" width="80" />
        <el-table-column prop="provinceName" label="省份" width="100" />
        <el-table-column prop="cityName" label="城市" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleDetail(row)">
              详情
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getScenicPage, deleteScenic } from '@/api/scenic'
import { getProvinceList } from '@/api/province'
import { getCityList } from '@/api/city'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const provinceList = ref([])
const cityList = ref([])

const searchForm = reactive({
  title: '',
  provinceId: '',
  cityId: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取城市列表
const fetchCityList = async (provinceId) => {
  if (!provinceId) {
    cityList.value = []
    return
  }
  
  try {
    const { data } = await getCityList(provinceId)
    cityList.value = data || []
  } catch (error) {
    console.error('获取城市列表失败:', error)
  }
}

// 省份变化处理
const handleProvinceChange = (provinceId) => {
  searchForm.cityId = ''
  fetchCityList(provinceId)
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const { data } = await getScenicPage(params)
    
    // 处理数据，添加省份和城市名称
    const scenics = data.records || []
    scenics.forEach(scenic => {
      const province = provinceList.value.find(p => p.id === scenic.provinceId)
      scenic.provinceName = province ? province.name : '未知'
      
      const city = cityList.value.find(c => c.id === scenic.cityId)
      scenic.cityName = city ? city.name : '未知'
    })
    
    tableData.value = scenics
    pagination.total = data.total || 0
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  cityList.value = []
  pagination.current = 1
  fetchData()
}

// 排序
const handleSortChange = ({ prop, order }) => {
  console.log('排序:', prop, order)
}

// 分页
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  fetchData()
}

// 操作
const handleCreate = () => {
  router.push('/scenic/create')
}

const handleDetail = (row) => {
  router.push(`/scenic/detail/${row.id}`)
}

const handleEdit = (row) => {
  router.push(`/scenic/edit/${row.id}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除景区"${row.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteScenic(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error(error)
    }
  }
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  fetchData()
})
</script>

<style scoped>
.scenic-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
