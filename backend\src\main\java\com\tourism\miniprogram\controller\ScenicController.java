package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Scenic;
import com.tourism.miniprogram.service.ScenicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 景区控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/scenics")
@Api(tags = "景区管理")
public class ScenicController {

    @Autowired
    private ScenicService scenicService;

    /**
     * 获取推荐景区列表
     *
     * @param provinceId 省份ID
     * @param cityId     城市ID
     * @param page       当前页
     * @param limit      每页大小
     * @return 景区分页列表
     */
    @GetMapping("/recommend")
    @ApiOperation(value = "获取推荐景区列表", notes = "分页获取推荐景区列表，支持按省份和城市筛选")
    public Result<IPage<Scenic>> getRecommendScenics(
            @ApiParam(value = "省份ID") @RequestParam(required = false) Integer provinceId,
            @ApiParam(value = "城市ID") @RequestParam(required = false) Integer cityId,
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long limit) {
        try {
            // 参数校验
            if (page < 1) {
                page = 1L;
            }
            if (limit < 1 || limit > 100) {
                limit = 10L;
            }
            
            IPage<Scenic> scenicPage = scenicService.getRecommendScenics(page, limit, provinceId, cityId);
            return Result.success(scenicPage);
        } catch (Exception e) {
            log.error("获取推荐景区列表失败，provinceId: {}, cityId: {}, page: {}, limit: {}", 
                     provinceId, cityId, page, limit, e);
            return Result.error("获取推荐景区列表失败");
        }
    }

    /**
     * 获取景区详情
     *
     * @param scenicId 景区ID
     * @return 景区详情
     */
    @GetMapping("/{scenicId}")
    @ApiOperation(value = "获取景区详情", notes = "根据景区ID获取景区详细信息")
    public Result<Scenic> getScenicDetail(
            @ApiParam(value = "景区ID", required = true) @PathVariable String scenicId) {
        try {
            Scenic scenic = scenicService.getScenicByScenicId(scenicId);
            if (scenic == null) {
                return Result.error(404, "景区不存在");
            }
            return Result.success(scenic);
        } catch (IllegalArgumentException e) {
            log.warn("参数错误: {}", e.getMessage());
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("获取景区详情失败，scenicId: {}", scenicId, e);
            return Result.error("获取景区详情失败");
        }
    }

    /**
     * 分页获取景区列表
     *
     * @param current    当前页
     * @param size       每页大小
     * @param title      景区标题（模糊查询）
     * @param provinceId 省份ID
     * @param cityId     城市ID
     * @return 景区分页列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取景区列表", notes = "分页获取景区列表，支持按标题、省份、城市筛选")
    public Result<IPage<Scenic>> getScenicPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "景区标题") @RequestParam(required = false) String title,
            @ApiParam(value = "省份ID") @RequestParam(required = false) Integer provinceId,
            @ApiParam(value = "城市ID") @RequestParam(required = false) Integer cityId) {
        try {
            Page<Scenic> page = new Page<>(current, size);
            QueryWrapper<Scenic> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(title)) {
                queryWrapper.like("title", title);
            }
            if (provinceId != null) {
                queryWrapper.eq("province_id", provinceId);
            }
            if (cityId != null) {
                queryWrapper.eq("city_id", cityId);
            }
            queryWrapper.orderByAsc("sort").orderByDesc("rating").orderByDesc("id");

            IPage<Scenic> scenicPage = scenicService.page(page, queryWrapper);
            return Result.success(scenicPage);
        } catch (Exception e) {
            log.error("分页获取景区列表失败", e);
            return Result.error("获取景区列表失败");
        }
    }

    /**
     * 根据ID获取景区详情
     *
     * @param id 景区ID
     * @return 景区详情
     */
    @GetMapping("/detail/{id}")
    @ApiOperation(value = "根据ID获取景区详情", notes = "根据数据库ID获取景区详细信息")
    public Result<Scenic> getScenicById(@ApiParam(value = "景区ID", required = true) @PathVariable Integer id) {
        try {
            Scenic scenic = scenicService.getById(id);
            if (scenic == null) {
                return Result.error(404, "景区不存在");
            }
            return Result.success(scenic);
        } catch (Exception e) {
            log.error("获取景区详情失败，id: {}", id, e);
            return Result.error("获取景区详情失败");
        }
    }

    /**
     * 创建景区
     *
     * @param scenic 景区信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建景区", notes = "创建新的景区")
    public Result<String> createScenic(@RequestBody @Valid Scenic scenic) {
        try {
            // 检查景区ID是否已存在
            QueryWrapper<Scenic> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("scenic_id", scenic.getScenicId());
            Scenic existScenic = scenicService.getOne(queryWrapper);
            if (existScenic != null) {
                return Result.error("景区ID已存在");
            }

            boolean success = scenicService.save(scenic);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建景区失败", e);
            return Result.error("创建景区失败");
        }
    }

    /**
     * 更新景区
     *
     * @param id     景区ID
     * @param scenic 景区信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新景区", notes = "更新景区信息")
    public Result<String> updateScenic(
            @ApiParam(value = "景区ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Scenic scenic) {
        try {
            Scenic existScenic = scenicService.getById(id);
            if (existScenic == null) {
                return Result.error(404, "景区不存在");
            }

            // 检查景区ID是否已被其他景区使用
            QueryWrapper<Scenic> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("scenic_id", scenic.getScenicId()).ne("id", id);
            Scenic duplicateScenic = scenicService.getOne(queryWrapper);
            if (duplicateScenic != null) {
                return Result.error("景区ID已被其他景区使用");
            }

            scenic.setId(id);
            boolean success = scenicService.updateById(scenic);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新景区失败，id: {}", id, e);
            return Result.error("更新景区失败");
        }
    }

    /**
     * 删除景区
     *
     * @param id 景区ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除景区", notes = "删除景区")
    public Result<String> deleteScenic(@ApiParam(value = "景区ID", required = true) @PathVariable Integer id) {
        try {
            Scenic scenic = scenicService.getById(id);
            if (scenic == null) {
                return Result.error(404, "景区不存在");
            }

            boolean success = scenicService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除景区失败，id: {}", id, e);
            return Result.error("删除景区失败");
        }
    }
}
