<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ScenicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tourism.miniprogram.entity.Scenic">
        <id column="id" property="id" />
        <result column="scenic_id" property="scenicId" />
        <result column="title" property="title" />
        <result column="subtitle" property="subtitle" />
        <result column="description" property="description" />
        <result column="image" property="image" />
        <result column="images" property="images" />
        <result column="open_time" property="openTime" />
        <result column="address" property="address" />
        <result column="province_id" property="provinceId" />
        <result column="city_id" property="cityId" />
        <result column="sort" property="sort" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scenic_id, title, subtitle, description, image, images,
        open_time, address, province_id, city_id,
        sort, status, created_at, updated_at
    </sql>

    <!-- 分页查询推荐景区列表 -->
    <select id="selectRecommendScenics" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM scenics
        WHERE status = 1
        <if test="provinceId != null">
            AND province_id = #{provinceId}
        </if>
        <if test="cityId != null">
            AND city_id = #{cityId}
        </if>
        ORDER BY sort ASC, id DESC
    </select>

    <!-- 根据景区ID获取景区详情 -->
    <select id="selectByScenicId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM scenics
        WHERE scenic_id = #{scenicId} AND status = 1
    </select>

</mapper>
