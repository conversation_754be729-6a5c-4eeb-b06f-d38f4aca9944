<template>
  <div class="guide-product-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑讲解产品' : '新增讲解产品' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="产品标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入产品标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="景区ID" prop="scenicId">
          <el-input-number
            v-model="form.scenicId"
            :min="1"
            placeholder="请输入景区ID"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="产品类型" prop="type">
          <el-input
            v-model="form.type"
            placeholder="请输入产品类型"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="讲解时长" prop="duration">
          <el-time-picker
            v-model="form.duration"
            placeholder="请选择讲解时长"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="产品价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入产品价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="背景图片" prop="backgroundImageUrl">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.backgroundImageUrl" :src="form.backgroundImageUrl" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="示例视频URL" prop="exampleVideoUrl">
          <el-input
            v-model="form.exampleVideoUrl"
            placeholder="请输入示例视频URL"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="讲解员ID" prop="lecturerId">
          <el-input-number
            v-model="form.lecturerId"
            :min="1"
            placeholder="请输入讲解员ID"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="地图URL" prop="mapUrl">
          <el-input
            v-model="form.mapUrl"
            placeholder="请输入地图URL"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="开始收听图片" prop="startListeningImageUrl">
          <el-input
            v-model="form.startListeningImageUrl"
            placeholder="请输入开始收听图片URL"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="产品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入产品描述"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { getGuideProductById, createGuideProduct, updateGuideProduct } from '@/api/guideProduct'
import { uploadImage } from '@/api/upload'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const formRef = ref()

const form = reactive({
  title: '',
  scenicId: null,
  type: '',
  duration: '',
  price: null,
  backgroundImageUrl: '',
  exampleVideoUrl: '',
  lecturerId: null,
  mapUrl: '',
  startListeningImageUrl: '',
  description: '',
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入产品标题', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  scenicId: [
    { required: true, message: '请输入景区ID', trigger: 'blur' },
    { type: 'number', min: 1, message: '景区ID必须大于0', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入产品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序必须在 0 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 图片上传
const handleImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      form.backgroundImageUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('图片上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error(error.message || '图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getGuideProductById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取讲解产品详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateGuideProduct(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createGuideProduct(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (isEdit.value) {
    fetchDetail()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/guide-product/list')
}

// 初始化
onMounted(() => {
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.guide-product-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader .image {
  width: 300px;
  height: 100px;
  display: block;
}

.image-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.image-uploader-loading {
  width: 300px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>
