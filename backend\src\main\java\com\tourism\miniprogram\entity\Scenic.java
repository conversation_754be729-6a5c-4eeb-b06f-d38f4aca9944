package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 景区实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("scenics")
@ApiModel(value = "Scenic对象", description = "景区信息")
public class Scenic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "景区ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "景区唯一标识")
    @TableField("scenic_id")
    @NotBlank(message = "景区ID不能为空")
    private String scenicId;

    @ApiModelProperty(value = "景区标题")
    @TableField("title")
    @NotBlank(message = "景区标题不能为空")
    private String title;

    @ApiModelProperty(value = "景区副标题")
    @TableField("subtitle")
    private String subtitle;

    @ApiModelProperty(value = "景区描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "主图片")
    @TableField("image")
    private String image;

    @ApiModelProperty(value = "图片集合(JSON格式)")
    @TableField("images")
    private String images;

    @ApiModelProperty(value = "价格")
    @TableField("price")
    private BigDecimal price;

    @ApiModelProperty(value = "评分")
    @TableField("rating")
    private BigDecimal rating;

    @ApiModelProperty(value = "开放时间")
    @TableField("open_time")
    private String openTime;

    @ApiModelProperty(value = "地址")
    @TableField("address")
    private String address;

    @ApiModelProperty(value = "联系电话")
    @TableField("phone")
    private String phone;

    @ApiModelProperty(value = "省份ID")
    @TableField("province_id")
    @NotNull(message = "省份ID不能为空")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    @TableField("city_id")
    @NotNull(message = "城市ID不能为空")
    private Integer cityId;

    @ApiModelProperty(value = "纬度")
    @TableField("latitude")
    private BigDecimal latitude;

    @ApiModelProperty(value = "经度")
    @TableField("longitude")
    private BigDecimal longitude;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
