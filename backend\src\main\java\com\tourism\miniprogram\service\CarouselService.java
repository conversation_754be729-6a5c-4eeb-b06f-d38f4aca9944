package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Carousel;

import java.util.List;

/**
 * 轮播图服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface CarouselService extends IService<Carousel> {

    /**
     * 根据条件获取轮播图列表
     *
     * @param provinceId 省份ID
     * @param type       类型
     * @return 轮播图列表
     */
    List<Carousel> getCarouselsByCondition(Integer provinceId, String type);

    /**
     * 获取首页轮播图列表
     *
     * @param provinceId 省份ID（可选）
     * @return 首页轮播图列表
     */
    List<Carousel> getHomeCarousels(Integer provinceId);

    /**
     * 获取启用状态的首页轮播图
     *
     * @param provinceId 省份ID（可选）
     * @param limit      限制数量
     * @return 首页轮播图列表
     */
    List<Carousel> getActiveHomeCarousels(Integer provinceId, Integer limit);

    /**
     * 更新轮播图排序
     *
     * @param id   轮播图ID
     * @param sort 新的排序值
     * @return 是否成功
     */
    boolean updateCarouselSort(Integer id, Integer sort);

    /**
     * 批量更新轮播图状态
     *
     * @param ids    轮播图ID列表
     * @param status 状态值
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Integer> ids, Integer status);
}
