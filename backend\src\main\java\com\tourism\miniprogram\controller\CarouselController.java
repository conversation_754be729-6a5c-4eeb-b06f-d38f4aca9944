package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Carousel;
import com.tourism.miniprogram.service.CarouselService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/carousels")
@Api(tags = "轮播图管理")
public class CarouselController {

    @Autowired
    private CarouselService carouselService;

    /**
     * 获取轮播图数据
     *
     * @param provinceId 省份ID
     * @param type       类型
     * @return 轮播图列表
     */
    @GetMapping
    @ApiOperation(value = "获取轮播图数据", notes = "根据省份和类型获取轮播图列表")
    public Result<List<Carousel>> getCarousels(
            @ApiParam(value = "省份ID") @RequestParam(required = false) Integer provinceId,
            @ApiParam(value = "类型", defaultValue = "home") @RequestParam(defaultValue = "home") String type) {
        try {
            List<Carousel> carousels = carouselService.getCarouselsByCondition(provinceId, type);
            return Result.success(carousels);
        } catch (Exception e) {
            log.error("获取轮播图数据失败，provinceId: {}, type: {}", provinceId, type, e);
            return Result.error("获取轮播图数据失败");
        }
    }

    /**
     * 分页获取轮播图列表
     *
     * @param current    当前页
     * @param size       每页大小
     * @param title      标题（模糊查询）
     * @param provinceId 省份ID
     * @param type       类型
     * @return 轮播图分页列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取轮播图列表", notes = "分页获取轮播图列表，支持按标题、省份、类型筛选")
    public Result<IPage<Carousel>> getCarouselPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "标题") @RequestParam(required = false) String title,
            @ApiParam(value = "省份ID") @RequestParam(required = false) Integer provinceId,
            @ApiParam(value = "类型") @RequestParam(required = false) String type) {
        try {
            Page<Carousel> page = new Page<>(current, size);
            QueryWrapper<Carousel> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(title)) {
                queryWrapper.like("title", title);
            }
            if (provinceId != null) {
                queryWrapper.eq("province_id", provinceId);
            }
            if (StringUtils.hasText(type)) {
                queryWrapper.eq("type", type);
            }
            queryWrapper.orderByAsc("sort").orderByDesc("id");

            IPage<Carousel> carouselPage = carouselService.page(page, queryWrapper);
            return Result.success(carouselPage);
        } catch (Exception e) {
            log.error("分页获取轮播图列表失败", e);
            return Result.error("获取轮播图列表失败");
        }
    }

    /**
     * 获取轮播图详情
     *
     * @param id 轮播图ID
     * @return 轮播图详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取轮播图详情", notes = "根据ID获取轮播图详细信息")
    public Result<Carousel> getCarouselById(@ApiParam(value = "轮播图ID", required = true) @PathVariable Integer id) {
        try {
            Carousel carousel = carouselService.getById(id);
            if (carousel == null) {
                return Result.error(404, "轮播图不存在");
            }
            return Result.success(carousel);
        } catch (Exception e) {
            log.error("获取轮播图详情失败，id: {}", id, e);
            return Result.error("获取轮播图详情失败");
        }
    }

    /**
     * 创建轮播图
     *
     * @param carousel 轮播图信息
     * @return 创建结果
     */
    @PostMapping
    @ApiOperation(value = "创建轮播图", notes = "创建新的轮播图")
    public Result<String> createCarousel(@RequestBody @Valid Carousel carousel) {
        try {
            boolean success = carouselService.save(carousel);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建轮播图失败", e);
            return Result.error("创建轮播图失败");
        }
    }

    /**
     * 更新轮播图
     *
     * @param id       轮播图ID
     * @param carousel 轮播图信息
     * @return 更新结果
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新轮播图", notes = "更新轮播图信息")
    public Result<String> updateCarousel(
            @ApiParam(value = "轮播图ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Carousel carousel) {
        try {
            Carousel existCarousel = carouselService.getById(id);
            if (existCarousel == null) {
                return Result.error(404, "轮播图不存在");
            }

            carousel.setId(id);
            boolean success = carouselService.updateById(carousel);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新轮播图失败，id: {}", id, e);
            return Result.error("更新轮播图失败");
        }
    }

    /**
     * 删除轮播图
     *
     * @param id 轮播图ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除轮播图", notes = "删除轮播图")
    public Result<String> deleteCarousel(@ApiParam(value = "轮播图ID", required = true) @PathVariable Integer id) {
        try {
            Carousel carousel = carouselService.getById(id);
            if (carousel == null) {
                return Result.error(404, "轮播图不存在");
            }

            boolean success = carouselService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除轮播图失败，id: {}", id, e);
            return Result.error("删除轮播图失败");
        }
    }

    /**
     * 获取首页轮播图
     *
     * @param provinceId 省份ID（可选）
     * @return 首页轮播图列表
     */
    @GetMapping("/home")
    @ApiOperation(value = "获取首页轮播图", notes = "获取首页轮播图列表，支持按省份筛选")
    public Result<List<Carousel>> getHomeCarousels(
            @ApiParam(value = "省份ID") @RequestParam(required = false) Integer provinceId) {
        try {
            List<Carousel> carousels = carouselService.getHomeCarousels(provinceId);
            return Result.success(carousels);
        } catch (Exception e) {
            log.error("获取首页轮播图失败，provinceId: {}", provinceId, e);
            return Result.error("获取首页轮播图失败");
        }
    }

    /**
     * 获取启用的首页轮播图
     *
     * @param provinceId 省份ID（可选）
     * @param limit      限制数量（可选）
     * @return 启用的首页轮播图列表
     */
    @GetMapping("/home/<USER>")
    @ApiOperation(value = "获取启用的首页轮播图", notes = "获取启用状态的首页轮播图，支持按省份筛选和数量限制")
    public Result<List<Carousel>> getActiveHomeCarousels(
            @ApiParam(value = "省份ID") @RequestParam(required = false) Integer provinceId,
            @ApiParam(value = "限制数量") @RequestParam(required = false) Integer limit) {
        try {
            List<Carousel> carousels = carouselService.getActiveHomeCarousels(provinceId, limit);
            return Result.success(carousels);
        } catch (Exception e) {
            log.error("获取启用的首页轮播图失败，provinceId: {}, limit: {}", provinceId, limit, e);
            return Result.error("获取启用的首页轮播图失败");
        }
    }

    /**
     * 更新轮播图排序
     *
     * @param id   轮播图ID
     * @param sort 新的排序值
     * @return 更新结果
     */
    @PutMapping("/{id}/sort")
    @ApiOperation(value = "更新轮播图排序", notes = "更新指定轮播图的排序值")
    public Result<String> updateCarouselSort(
            @ApiParam(value = "轮播图ID", required = true) @PathVariable Integer id,
            @ApiParam(value = "排序值", required = true) @RequestParam Integer sort) {
        try {
            boolean success = carouselService.updateCarouselSort(id, sort);
            if (success) {
                return Result.success("排序更新成功");
            } else {
                return Result.error("排序更新失败");
            }
        } catch (Exception e) {
            log.error("更新轮播图排序失败，id: {}, sort: {}", id, sort, e);
            return Result.error("更新轮播图排序失败");
        }
    }

    /**
     * 批量更新轮播图状态
     *
     * @param ids    轮播图ID列表（逗号分隔）
     * @param status 状态值
     * @return 更新结果
     */
    @PutMapping("/batch/status")
    @ApiOperation(value = "批量更新轮播图状态", notes = "批量更新多个轮播图的状态")
    public Result<String> batchUpdateStatus(
            @ApiParam(value = "轮播图ID列表，逗号分隔", required = true) @RequestParam String ids,
            @ApiParam(value = "状态值：1-启用，0-禁用", required = true) @RequestParam Integer status) {
        try {
            List<Integer> idList = Arrays.stream(ids.split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            boolean success = carouselService.batchUpdateStatus(idList, status);
            if (success) {
                return Result.success("状态更新成功");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新轮播图状态失败，ids: {}, status: {}", ids, status, e);
            return Result.error("批量更新轮播图状态失败");
        }
    }
}
