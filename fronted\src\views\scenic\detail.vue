<template>
  <div class="scenic-detail">
    <div class="page-header">
      <h2>景区详情</h2>
      <div>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="景区ID">
          {{ detail.scenicId }}
        </el-descriptions-item>
        <el-descriptions-item label="景区标题">
          {{ detail.title }}
        </el-descriptions-item>
        <el-descriptions-item label="副标题" :span="2">
          {{ detail.subtitle }}
        </el-descriptions-item>
        <el-descriptions-item label="景区描述" :span="2">
          {{ detail.description }}
        </el-descriptions-item>
        <el-descriptions-item label="封面图片" :span="2">
          <el-image
            v-if="detail.image"
            :src="detail.image"
            style="width: 200px; height: 150px"
            fit="cover"
            :preview-src-list="[detail.image]"
          />
        </el-descriptions-item>
        <el-descriptions-item label="相册图片" :span="2">
          <div v-if="imagesList.length > 0" class="images-gallery">
            <el-image
              v-for="(image, index) in imagesList"
              :key="index"
              :src="image"
              style="width: 100px; height: 80px; margin-right: 10px"
              fit="cover"
              :preview-src-list="imagesList"
            />
          </div>
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="价格">
          ¥{{ detail.price }}
        </el-descriptions-item>
        <el-descriptions-item label="评分">
          {{ detail.rating }}分
        </el-descriptions-item>
        <el-descriptions-item label="开放时间">
          {{ detail.openTime }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ detail.phone }}
        </el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">
          {{ detail.address }}
        </el-descriptions-item>
        <el-descriptions-item label="所属省份">
          {{ detail.provinceName }}
        </el-descriptions-item>
        <el-descriptions-item label="所属城市">
          {{ detail.cityName }}
        </el-descriptions-item>
        <el-descriptions-item label="坐标">
          {{ detail.latitude }}, {{ detail.longitude }}
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sort }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
            {{ detail.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detail.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detail.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getScenicInfo } from '@/api/scenic'
import { getProvinceList } from '@/api/province'
import { getCityList } from '@/api/city'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const provinceList = ref([])
const cityList = ref([])
const imagesList = ref([])

const detail = reactive({
  id: '',
  scenicId: '',
  title: '',
  subtitle: '',
  description: '',
  image: '',
  images: '',
  price: 0,
  rating: 0,
  openTime: '',
  address: '',
  phone: '',
  provinceId: '',
  cityId: '',
  provinceName: '',
  cityName: '',
  latitude: null,
  longitude: null,
  sort: 0,
  status: 1,
  createdAt: '',
  updatedAt: ''
})

// 获取省份和城市列表
const fetchLocationData = async () => {
  try {
    const { data: provinces } = await getProvinceList()
    provinceList.value = provinces || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true
  try {
    const { data } = await getScenicInfo(route.params.id)
    Object.assign(detail, data)
    
    // 设置省份名称
    const province = provinceList.value.find(p => p.id === data.provinceId)
    detail.provinceName = province ? province.name : '未知'
    
    // 获取城市列表并设置城市名称
    if (data.provinceId) {
      const { data: cities } = await getCityList(data.provinceId)
      cityList.value = cities || []
      const city = cityList.value.find(c => c.id === data.cityId)
      detail.cityName = city ? city.name : '未知'
    }
    
    // 处理相册图片
    if (data.images) {
      try {
        imagesList.value = JSON.parse(data.images)
      } catch (e) {
        console.error('解析相册图片失败:', e)
        imagesList.value = []
      }
    }
  } catch (error) {
    ElMessage.error('获取景区详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/scenic/edit/${route.params.id}`)
}

// 返回列表
const handleBack = () => {
  router.push('/scenic/list')
}

// 初始化
onMounted(async () => {
  await fetchLocationData()
  fetchDetail()
})
</script>

<style scoped>
.scenic-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.images-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
</style>
