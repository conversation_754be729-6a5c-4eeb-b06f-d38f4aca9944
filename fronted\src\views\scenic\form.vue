<template>
  <div class="scenic-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑景区' : '新增景区' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 800px"
      >
        <el-form-item label="景区ID" prop="scenicId">
          <el-input
            v-model="form.scenicId"
            placeholder="系统自动生成"
            :readonly="true"
            style="background-color: #f5f7fa;"
          />
          <div class="form-tip">景区唯一标识，系统自动生成</div>
        </el-form-item>

        <el-form-item label="景区标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入景区标题"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="副标题" prop="subtitle">
          <el-input
            v-model="form.subtitle"
            placeholder="请输入副标题"
            maxlength="25"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="景区描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入景区描述"
            maxlength="25"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属省份" prop="provinceId">
              <el-select
                v-model="form.provinceId"
                placeholder="请选择省份"
                style="width: 100%"
                @change="handleProvinceChange"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.id"
                  :label="province.name"
                  :value="province.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属城市" prop="cityId">
              <el-select
                v-model="form.cityId"
                placeholder="请选择城市"
                style="width: 100%"
              >
                <el-option
                  v-for="city in cityList"
                  :key="city.id"
                  :label="city.name"
                  :value="city.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="封面图片" prop="image">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.image" :src="form.image" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="相册图片">
          <el-upload
            class="images-uploader"
            action="#"
            list-type="picture-card"
            :file-list="imagesList"
            :before-upload="beforeImageUpload"
            :http-request="handleImagesUpload"
            :on-remove="handleImageRemove"
            :disabled="imagesUploadLoading"
            multiple
          >
            <div v-if="imagesUploadLoading" class="upload-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">可上传多张图片，支持jpg/png/gif/webp格式，单张不超过2MB</div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="价格" prop="price">
              <el-input-number
                v-model="form.price"
                :min="0"
                :precision="2"
                placeholder="请输入价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="评分" prop="rating">
              <el-input-number
                v-model="form.rating"
                :min="0"
                :max="5"
                :precision="1"
                placeholder="请输入评分"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                :min="0"
                :max="999"
                placeholder="请输入排序"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="开放时间" prop="openTime">
          <el-input
            v-model="form.openTime"
            placeholder="请输入开放时间"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input
            v-model="form.address"
            placeholder="请输入地址"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入联系电话"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input-number
                v-model="form.latitude"
                :precision="6"
                placeholder="请输入纬度"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input-number
                v-model="form.longitude"
                :precision="6"
                placeholder="请输入经度"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { getScenicInfo, createScenic, updateScenic } from '@/api/scenic'
import { getProvinceList } from '@/api/province'
import { getCityList } from '@/api/city'
import { uploadImage } from '@/api/upload'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const imagesUploadLoading = ref(false)
const formRef = ref()
const provinceList = ref([])
const cityList = ref([])
const imagesList = ref([])

const form = reactive({
  scenicId: '',
  title: '',
  subtitle: '',
  description: '',
  image: '',
  images: '',
  price: 0,
  rating: 0,
  openTime: '',
  address: '',
  phone: '',
  provinceId: '',
  cityId: '',
  latitude: null,
  longitude: null,
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  scenicId: [
    { required: true, message: '景区ID不能为空', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入景区标题', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  provinceId: [
    { required: true, message: '请选择所属省份', trigger: 'change' }
  ],
  cityId: [
    { required: true, message: '请选择所属城市', trigger: 'change' }
  ]
}

// 生成随机景区ID
const generateScenicId = () => {
  const timestamp = Date.now().toString()
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `scenic_${timestamp}_${randomStr}`
}

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取城市列表
const fetchCityList = async (provinceId) => {
  if (!provinceId) {
    cityList.value = []
    return
  }
  
  try {
    const { data } = await getCityList(provinceId)
    cityList.value = data || []
  } catch (error) {
    console.error('获取城市列表失败:', error)
  }
}

// 省份变化处理
const handleProvinceChange = (provinceId) => {
  form.cityId = ''
  fetchCityList(provinceId)
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 封面图片上传
const handleImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    // 显示上传进度
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '封面图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 调用上传API
    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`封面图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      // 使用预览URL作为图片地址
      form.image = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('封面图片上传成功')

      // 打印上传结果信息（用于调试）
      console.log('封面图片上传成功:', {
        fileId: response.data.fileId,
        originalFileName: response.data.originalFileName,
        fileSize: response.data.fileSize,
        previewUrl: response.data.previewUrl,
        downloadUrl: response.data.downloadUrl,
        cdnUrl: response.data.cdnUrl
      })
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('封面图片上传失败:', error)
    ElMessage.error(error.message || '封面图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 相册图片上传
const handleImagesUpload = async (options) => {
  const { file } = options

  try {
    imagesUploadLoading.value = true

    // 显示上传进度
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '相册图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 调用上传API
    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`相册图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      // 使用预览URL作为图片地址
      const imageUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl

      imagesList.value.push({
        name: response.data.originalFileName || file.name,
        url: imageUrl,
        uid: Date.now() + Math.random() // 添加唯一标识
      })

      updateImagesField()
      ElMessage.success('相册图片上传成功')

      // 打印上传结果信息（用于调试）
      console.log('相册图片上传成功:', {
        fileId: response.data.fileId,
        originalFileName: response.data.originalFileName,
        fileSize: response.data.fileSize,
        previewUrl: response.data.previewUrl,
        downloadUrl: response.data.downloadUrl,
        cdnUrl: response.data.cdnUrl
      })
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('相册图片上传失败:', error)
    ElMessage.error(error.message || '相册图片上传失败，请重试')
  } finally {
    imagesUploadLoading.value = false
  }
}

// 移除图片
const handleImageRemove = (file) => {
  const index = imagesList.value.findIndex(item => item.url === file.url)
  if (index > -1) {
    imagesList.value.splice(index, 1)
    updateImagesField()
  }
}

// 更新images字段
const updateImagesField = () => {
  const urls = imagesList.value.map(item => item.url)
  form.images = JSON.stringify(urls)
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getScenicInfo(route.params.id)
    Object.assign(form, data)
    
    // 处理相册图片
    if (data.images) {
      try {
        const urls = JSON.parse(data.images)
        imagesList.value = urls.map((url, index) => ({
          name: `image_${index}`,
          url
        }))
      } catch (e) {
        console.error('解析相册图片失败:', e)
      }
    }
    
    // 获取对应的城市列表
    if (data.provinceId) {
      await fetchCityList(data.provinceId)
    }
  } catch (error) {
    ElMessage.error('获取景区详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateScenic(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createScenic(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  imagesList.value = []
  if (isEdit.value) {
    fetchDetail()
  } else {
    // 新增模式下重新生成景区ID
    form.scenicId = generateScenicId()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/scenic/list')
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  if (isEdit.value) {
    fetchDetail()
  } else {
    // 新增模式下生成景区ID
    form.scenicId = generateScenicId()
  }
})
</script>

<style scoped>
.scenic-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader .image {
  width: 178px;
  height: 178px;
  display: block;
}

.image-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.image-uploader-loading {
  width: 178px;
  height: 178px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
  padding: 20px;
}

.upload-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.images-uploader {
  margin-bottom: 10px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}
</style>
