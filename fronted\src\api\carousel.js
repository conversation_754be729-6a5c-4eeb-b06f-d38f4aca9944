import request from '@/utils/request'

/**
 * 获取轮播图数据
 * @param {Object} params 查询参数
 * @param {number} params.provinceId 省份ID
 * @param {string} params.type 类型
 */
export function getCarouselList(params) {
  return request({
    url: '/carousels',
    method: 'get',
    params
  })
}

/**
 * 分页获取轮播图列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @param {string} params.title 标题
 * @param {number} params.provinceId 省份ID
 * @param {string} params.type 类型
 */
export function getCarouselPage(params) {
  return request({
    url: '/carousels/page',
    method: 'get',
    params
  })
}

/**
 * 获取轮播图详情
 * @param {number} id 轮播图ID
 */
export function getCarouselInfo(id) {
  return request({
    url: `/carousels/${id}`,
    method: 'get'
  })
}

/**
 * 创建轮播图
 * @param {Object} data 轮播图数据
 */
export function createCarousel(data) {
  return request({
    url: '/carousels',
    method: 'post',
    data
  })
}

/**
 * 更新轮播图
 * @param {number} id 轮播图ID
 * @param {Object} data 轮播图数据
 */
export function updateCarousel(id, data) {
  return request({
    url: `/carousels/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除轮播图
 * @param {number} id 轮播图ID
 */
export function deleteCarousel(id) {
  return request({
    url: `/carousels/${id}`,
    method: 'delete'
  })
}

/**
 * 获取首页轮播图列表
 * @param {Object} params 查询参数
 * @param {number} params.provinceId 省份ID（可选）
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 */
export function getHomeCarousels(params) {
  return request({
    url: '/carousels/home',
    method: 'get',
    params
  })
}

/**
 * 获取启用的首页轮播图
 * @param {Object} params 查询参数
 * @param {number} params.provinceId 省份ID（可选）
 * @param {number} params.limit 限制数量（可选）
 */
export function getActiveHomeCarousels(params) {
  return request({
    url: '/carousels/home/<USER>',
    method: 'get',
    params
  })
}

/**
 * 更新轮播图排序
 * @param {number} id 轮播图ID
 * @param {number} sort 排序值
 */
export function updateCarouselSort(id, sort) {
  return request({
    url: `/carousels/${id}/sort`,
    method: 'put',
    params: { sort }
  })
}

/**
 * 批量更新轮播图状态
 * @param {Array} ids 轮播图ID列表
 * @param {number} status 状态值
 */
export function batchUpdateCarouselStatus(ids, status) {
  return request({
    url: '/carousels/batch/status',
    method: 'put',
    params: {
      ids: ids.join(','),
      status
    }
  })
}
