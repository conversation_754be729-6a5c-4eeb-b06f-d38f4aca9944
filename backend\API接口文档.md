# 旅游讲解小程序API接口文档

## 接口概览

本文档描述了旅游讲解小程序后端服务的所有API接口。所有接口都返回统一的JSON格式响应。

### 基础信息

- **基础URL**: `http://localhost:8080/api`
- **API文档**: `http://localhost:8080/api/doc.html`
- **响应格式**: JSON
- **字符编码**: UTF-8

### 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1701234567890
}
```

## 1. 省份接口

### 1.1 获取省份列表

**接口地址**: `GET /provinces`

**接口描述**: 获取所有启用的省份列表

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "北京市",
      "code": "BJ",
      "sort": 1,
      "status": 1,
      "createdAt": "2023-12-01T10:00:00",
      "updatedAt": "2023-12-01T10:00:00"
    }
  ]
}
```

## 2. 城市接口

### 2.1 获取城市列表

**接口地址**: `GET /cities`

**接口描述**: 根据省份ID获取城市列表

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province_id | Integer | 是 | 省份ID |

**请求示例**: `GET /cities?province_id=1`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "北京市",
      "provinceId": 1,
      "code": "BJ01",
      "sort": 1,
      "status": 1,
      "createdAt": "2023-12-01T10:00:00",
      "updatedAt": "2023-12-01T10:00:00"
    }
  ]
}
```

## 3. 轮播图接口

### 3.1 获取轮播图数据

**接口地址**: `GET /carousels`

**接口描述**: 根据省份和类型获取轮播图列表

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| province_id | Integer | 否 | - | 省份ID |
| type | String | 否 | home | 类型（home-首页） |

**请求示例**: `GET /carousels?province_id=1&type=home`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "title": "美丽的天安门",
      "subtitle": "北京著名景点",
      "image": "https://example.com/image1.jpg",
      "scenicId": "scenic_001",
      "provinceId": 1,
      "type": "home",
      "sort": 1,
      "status": 1,
      "createdAt": "2023-12-01T10:00:00",
      "updatedAt": "2023-12-01T10:00:00"
    }
  ]
}
```

## 4. 景区接口

### 4.1 获取推荐景区列表

**接口地址**: `GET /scenics/recommend`

**接口描述**: 分页获取推荐景区列表，支持按省份和城市筛选

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| province_id | Integer | 否 | - | 省份ID |
| city_id | Integer | 否 | - | 城市ID |
| page | Long | 否 | 1 | 当前页 |
| limit | Long | 否 | 10 | 每页大小 |

**请求示例**: `GET /scenics/recommend?province_id=1&city_id=1&page=1&limit=10`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "scenicId": "scenic_001",
        "title": "天安门广场",
        "subtitle": "中华人民共和国的象征",
        "description": "天安门广场位于北京市中心...",
        "image": "https://example.com/scenic1.jpg",
        "images": "[\"https://example.com/1.jpg\", \"https://example.com/2.jpg\"]",
        "price": 0.00,
        "rating": 4.8,
        "openTime": "全天开放",
        "address": "北京市东城区天安门广场",
        "phone": "010-12345678",
        "provinceId": 1,
        "cityId": 1,
        "latitude": 39.903728,
        "longitude": 116.391248,
        "sort": 1,
        "status": 1,
        "createdAt": "2023-12-01T10:00:00",
        "updatedAt": "2023-12-01T10:00:00"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 4.2 获取景区详情

**接口地址**: `GET /scenics/{scenic_id}`

**接口描述**: 根据景区ID获取景区详细信息

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scenic_id | String | 是 | 景区ID |

**请求示例**: `GET /scenics/scenic_001`

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "scenicId": "scenic_001",
    "title": "天安门广场",
    "subtitle": "中华人民共和国的象征",
    "description": "天安门广场位于北京市中心，是世界上最大的城市广场之一...",
    "image": "https://example.com/scenic1.jpg",
    "images": "[\"https://example.com/1.jpg\", \"https://example.com/2.jpg\"]",
    "price": 0.00,
    "rating": 4.8,
    "openTime": "全天开放",
    "address": "北京市东城区天安门广场",
    "phone": "010-12345678",
    "provinceId": 1,
    "cityId": 1,
    "latitude": 39.903728,
    "longitude": 116.391248,
    "sort": 1,
    "status": 1,
    "createdAt": "2023-12-01T10:00:00",
    "updatedAt": "2023-12-01T10:00:00"
  }
}
```

## 5. 用户接口

### 5.1 获取当前用户信息

**接口地址**: `GET /user/info`

**接口描述**: 根据JWT令牌获取当前用户信息

**请求头**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer {token} |

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "openid": "wx_openid_123",
    "nickname": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "phone": "13800138000",
    "province": "北京市",
    "city": "北京市",
    "status": 1,
    "createdAt": "2023-12-01T10:00:00",
    "updatedAt": "2023-12-01T10:00:00"
  }
}
```

### 5.2 更新当前用户信息

**接口地址**: `PUT /user/info`

**接口描述**: 根据JWT令牌更新当前用户信息

**请求头**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | String | 是 | Bearer {token} |

**请求体**:
```json
{
  "nickname": "李四",
  "avatar": "https://example.com/new-avatar.jpg",
  "phone": "13900139000",
  "province": "上海市",
  "city": "上海市"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

## 6. 认证接口

### 6.1 微信小程序登录

**接口地址**: `POST /auth/wechat/login`

**接口描述**: 通过微信授权码进行登录

**请求体**:
```json
{
  "code": "微信授权码",
  "nickname": "用户昵称",
  "avatarUrl": "头像URL",
  "region": "用户地区",
  "phone": "手机号码"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "userId": 1,
    "token": "JWT令牌",
    "nickname": "用户昵称",
    "avatarUrl": "头像URL",
    "region": "用户地区",
    "phone": "手机号码",
    "isNewUser": false
  }
}
```

### 6.2 测试接口

**接口地址**: `GET /auth/test`

**接口描述**: 用于测试服务是否正常

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "服务正常运行"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权或令牌无效 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **无需认证的接口**: 省份、城市、轮播图、景区相关接口无需JWT认证
2. **需要认证的接口**: 用户信息相关接口需要在请求头中携带JWT令牌
3. **分页参数**: page从1开始，limit最大值为100
4. **图片字段**: images字段为JSON格式的字符串数组
5. **时间格式**: 所有时间字段均为ISO 8601格式
