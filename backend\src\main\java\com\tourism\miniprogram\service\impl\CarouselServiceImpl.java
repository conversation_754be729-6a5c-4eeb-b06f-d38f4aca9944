package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Carousel;
import com.tourism.miniprogram.mapper.CarouselMapper;
import com.tourism.miniprogram.service.CarouselService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 轮播图服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class CarouselServiceImpl extends ServiceImpl<CarouselMapper, Carousel> implements CarouselService {

    @Override
    public List<Carousel> getCarouselsByCondition(Integer provinceId, String type) {
        try {
            return baseMapper.selectCarouselsByCondition(provinceId, type);
        } catch (Exception e) {
            log.error("获取轮播图列表失败，provinceId: {}, type: {}", provinceId, type, e);
            throw new RuntimeException("获取轮播图列表失败");
        }
    }

    @Override
    public List<Carousel> getHomeCarousels(Integer provinceId) {
        try {
            QueryWrapper<Carousel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("type", "home");
            if (provinceId != null) {
                queryWrapper.eq("province_id", provinceId);
            }
            queryWrapper.orderByAsc("sort").orderByDesc("id");
            return list(queryWrapper);
        } catch (Exception e) {
            log.error("获取首页轮播图失败，provinceId: {}", provinceId, e);
            throw new RuntimeException("获取首页轮播图失败");
        }
    }

    @Override
    public List<Carousel> getActiveHomeCarousels(Integer provinceId, Integer limit) {
        try {
            QueryWrapper<Carousel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("type", "home")
                       .eq("status", 1);
            if (provinceId != null) {
                queryWrapper.eq("province_id", provinceId);
            }
            queryWrapper.orderByAsc("sort").orderByDesc("id");
            if (limit != null && limit > 0) {
                queryWrapper.last("LIMIT " + limit);
            }
            return list(queryWrapper);
        } catch (Exception e) {
            log.error("获取启用的首页轮播图失败，provinceId: {}, limit: {}", provinceId, limit, e);
            throw new RuntimeException("获取启用的首页轮播图失败");
        }
    }

    @Override
    public boolean updateCarouselSort(Integer id, Integer sort) {
        try {
            UpdateWrapper<Carousel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id).set("sort", sort);
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("更新轮播图排序失败，id: {}, sort: {}", id, sort, e);
            throw new RuntimeException("更新轮播图排序失败");
        }
    }

    @Override
    public boolean batchUpdateStatus(List<Integer> ids, Integer status) {
        try {
            UpdateWrapper<Carousel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", ids).set("status", status);
            return update(updateWrapper);
        } catch (Exception e) {
            log.error("批量更新轮播图状态失败，ids: {}, status: {}", ids, status, e);
            throw new RuntimeException("批量更新轮播图状态失败");
        }
    }
}
