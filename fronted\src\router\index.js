import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '仪表盘', icon: 'House' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    meta: { title: '用户管理', icon: 'User' },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/list.vue'),
        meta: { title: '用户列表', icon: 'UserFilled' }
      },
      {
        path: 'detail/:id',
        name: 'UserDetail',
        component: () => import('@/views/user/detail.vue'),
        meta: { title: '用户详情', hidden: true },
        props: true
      },
      {
        path: 'edit/:id',
        name: 'UserEdit',
        component: () => import('@/views/user/edit.vue'),
        meta: { title: '编辑用户', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/province',
    component: Layout,
    redirect: '/province/list',
    meta: { title: '省份管理', icon: 'Location' },
    children: [
      {
        path: 'list',
        name: 'ProvinceList',
        component: () => import('@/views/province/list.vue'),
        meta: { title: '省份列表', icon: 'LocationFilled' }
      },
      {
        path: 'create',
        name: 'ProvinceCreate',
        component: () => import('@/views/province/form.vue'),
        meta: { title: '新增省份', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ProvinceEdit',
        component: () => import('@/views/province/form.vue'),
        meta: { title: '编辑省份', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ProvinceDetail',
        component: () => import('@/views/province/detail.vue'),
        meta: { title: '省份详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/city',
    component: Layout,
    redirect: '/city/list',
    meta: { title: '城市管理', icon: 'OfficeBuilding' },
    children: [
      {
        path: 'list',
        name: 'CityList',
        component: () => import('@/views/city/list.vue'),
        meta: { title: '城市列表', icon: 'Building' }
      },
      {
        path: 'create',
        name: 'CityCreate',
        component: () => import('@/views/city/form.vue'),
        meta: { title: '新增城市', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'CityEdit',
        component: () => import('@/views/city/form.vue'),
        meta: { title: '编辑城市', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'CityDetail',
        component: () => import('@/views/city/detail.vue'),
        meta: { title: '城市详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/scenic',
    component: Layout,
    redirect: '/scenic/list',
    meta: { title: '景区管理', icon: 'MapLocation' },
    children: [
      {
        path: 'list',
        name: 'ScenicList',
        component: () => import('@/views/scenic/list.vue'),
        meta: { title: '景区列表', icon: 'Place' }
      },
      {
        path: 'create',
        name: 'ScenicCreate',
        component: () => import('@/views/scenic/form.vue'),
        meta: { title: '新增景区', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ScenicEdit',
        component: () => import('@/views/scenic/form.vue'),
        meta: { title: '编辑景区', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ScenicDetail',
        component: () => import('@/views/scenic/detail.vue'),
        meta: { title: '景区详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/carousel',
    component: Layout,
    redirect: '/carousel/list',
    meta: { title: '轮播图管理', icon: 'Picture' },
    children: [
      {
        path: 'list',
        name: 'CarouselList',
        component: () => import('@/views/carousel/list.vue'),
        meta: { title: '轮播图列表', icon: 'PictureFilled' }
      },
      {
        path: 'create',
        name: 'CarouselCreate',
        component: () => import('@/views/carousel/form.vue'),
        meta: { title: '新增轮播图', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'CarouselEdit',
        component: () => import('@/views/carousel/form.vue'),
        meta: { title: '编辑轮播图', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'CarouselDetail',
        component: () => import('@/views/carousel/detail.vue'),
        meta: { title: '轮播图详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/home-carousel',
    component: Layout,
    meta: { title: '首页轮播图管理', icon: 'Monitor' },
    children: [
      {
        path: '',
        name: 'HomeCarousel',
        component: () => import('@/views/HomeCarousel.vue'),
        meta: { title: '首页轮播图管理', icon: 'Monitor' }
      }
    ]
  },
  {
    path: '/review',
    component: Layout,
    redirect: '/review/list',
    meta: { title: '评价管理', icon: 'ChatDotRound' },
    children: [
      {
        path: 'list',
        name: 'ReviewList',
        component: () => import('@/views/review/list.vue'),
        meta: { title: '评价列表', icon: 'Comment' }
      },
      {
        path: 'create',
        name: 'ReviewCreate',
        component: () => import('@/views/review/form.vue'),
        meta: { title: '新增评价', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'ReviewEdit',
        component: () => import('@/views/review/form.vue'),
        meta: { title: '编辑评价', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'ReviewDetail',
        component: () => import('@/views/review/detail.vue'),
        meta: { title: '评价详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/guide-product',
    component: Layout,
    redirect: '/guide-product/list',
    meta: { title: '讲解产品管理', icon: 'Headset' },
    children: [
      {
        path: 'list',
        name: 'GuideProductList',
        component: () => import('@/views/guide-product/list.vue'),
        meta: { title: '讲解产品列表', icon: 'Service' }
      },
      {
        path: 'create',
        name: 'GuideProductCreate',
        component: () => import('@/views/guide-product/form.vue'),
        meta: { title: '新增讲解产品', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'GuideProductEdit',
        component: () => import('@/views/guide-product/form.vue'),
        meta: { title: '编辑讲解产品', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'GuideProductDetail',
        component: () => import('@/views/guide-product/detail.vue'),
        meta: { title: '讲解产品详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/guide-point',
    component: Layout,
    redirect: '/guide-point/list',
    meta: { title: '讲解点管理', icon: 'LocationInformation' },
    children: [
      {
        path: 'list',
        name: 'GuidePointList',
        component: () => import('@/views/guide-point/list.vue'),
        meta: { title: '讲解点列表', icon: 'Position' }
      },
      {
        path: 'create',
        name: 'GuidePointCreate',
        component: () => import('@/views/guide-point/form.vue'),
        meta: { title: '新增讲解点', hidden: true }
      },
      {
        path: 'edit/:id',
        name: 'GuidePointEdit',
        component: () => import('@/views/guide-point/form.vue'),
        meta: { title: '编辑讲解点', hidden: true },
        props: true
      },
      {
        path: 'detail/:id',
        name: 'GuidePointDetail',
        component: () => import('@/views/guide-point/detail.vue'),
        meta: { title: '讲解点详情', hidden: true },
        props: true
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '404', hidden: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 旅游讲解小程序管理后台` : '旅游讲解小程序管理后台'
  next()
})

export default router
